/**
 * Actions 管理
 * 简化的action管理系统，只维护翻译相关的actions
 */

// 导入翻译actions
import { getTranslationActions } from './translationActions'

// Action基础类型定义
export interface BaseAction {
  name: string;
  handler: (...args: any[]) => void;
  description?: string;
}

/**
 * 创建划词工具栏数据更新action
 * 用于处理和拼接流式消息数据
 */
export const createUpdateModalDataAction = (): BaseAction => {
  return {
    name: 'updateModalData',
    description: '更新和拼接处理流式消息数据，用于划词工具栏显示',
    handler: async (response: any) => {
      console.log('updateModalData handler:', response);

      // 处理流式数据更新逻辑
      if (response && response.list && Array.isArray(response.list)) {
        // 提取流式消息内容
        const streamTexts = response.list
          .filter((item: any) => item.content && item.content.text)
          .map((item: any) => item.content.text);

        // 拼接流式内容
        const concatenatedText = streamTexts.join('');

        if (concatenatedText) {
          // 更新模态框数据
          console.log('更新模态框数据:', concatenatedText);

          // 这里可以通过事件或回调机制更新UI
          // 例如：触发自定义事件或调用回调函数
          const updateEvent = new CustomEvent('modalDataUpdate', {
            detail: {
              data: concatenatedText,
              isComplete: response.endFlag || false
            }
          });
          window.dispatchEvent(updateEvent);
        }
      }
    }
  };
};

/**
 * 获取所有可用的actions
 */
export const getActionsForChatUI = (): BaseAction[] => {
  return [
    ...getTranslationActions(),
    createUpdateModalDataAction()
  ]
}

// 默认导出所有actions（用于ChatUI）
export default getActionsForChatUI
