import React, { useEffect, useRef, useState } from 'react';
import { LeftOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import { Divider } from 'antd';
import { chatLogo } from '../../../common/images';
import * as styles from "./index.module.less";
import { getAIService } from '../../config/aiConfig';
import SecondaryActionButtons from '../SecondaryActionButtons';
import type { SecondaryAction } from '../../../config/menuItems';
import { textOperations } from '../../utils/textOperations';

interface AIProcessModalProps {
  isVisible: boolean;
  selectedText: string;
  actionType: string;
  onClose: () => void;
  position?: { x: number; y: number };
  onRegisterDataUpdater?: (updater: (data: string, isComplete: boolean) => void) => void;
}

const AIProcessModal: React.FC<AIProcessModalProps> = ({
  isVisible,
  selectedText,
  actionType,
  onClose,
  position = { x: 0, y: 0 },
  onRegisterDataUpdater
}) => {
  // 自定义状态管理，替代 useAIProcess hook
  const [state, setState] = useState({
    isProcessing: false,
    content: '',
    isComplete: false,
    error: null as string | null,
    processingTime: 0,
  });

  const modalRef = useRef<HTMLDivElement>(null);
  const aiService = getAIService();
  const processStartedRef = useRef<string>(''); // 记录已启动的处理
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [isThinkingExpanded, setIsThinkingExpanded] = useState(false); // 思考过程展开状态
  const [showContinueInput, setShowContinueInput] = useState(false); // 是否显示继续问输入框
  const [continueQuestion, setContinueQuestion] = useState(''); // 继续问的内容
  const [isClosing, setIsClosing] = useState(false); // 是否正在关闭（用于淡出动画）

  // 获取操作类型的中文名称
  const getActionName = (action: string): string => {
    return aiService.getActionName(action);
  };

  // 自定义状态管理函数
  const startProcess = () => {
    setState({
      isProcessing: true,
      content: '',
      isComplete: false,
      error: null,
      processingTime: 0,
    });

    // 启动计时器
    timerRef.current = setInterval(() => {
      setState(prev => ({
        ...prev,
        processingTime: prev.processingTime + 1
      }));
    }, 1000);
  };

  const stopProcess = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    setState(prev => ({
      ...prev,
      isProcessing: false,
    }));
  };

  const resetState = () => {
    setState({
      isProcessing: false,
      content: '',
      isComplete: false,
      error: null,
      processingTime: 0,
    });
  };

  // 处理关闭
  const handleClose = () => {
    setIsClosing(true);
    // 延迟执行关闭，让淡出动画播放
    setTimeout(() => {
      stopProcess();
      resetState();
      setIsClosing(false);
      onClose();
    }, 200); // 与CSS动画时长匹配
  };



  // 继续问
  const handleContinueAsk = () => {
    console.log('handleContinueAsk');
    setShowContinueInput(true);
  };

  // 发送继续问题
  const handleSendContinueQuestion = () => {
    if (!continueQuestion.trim()) return;

    // 向背景脚本发送消息，请求打开侧边栏并传递问题
    chrome.runtime.sendMessage({
      action: 'continue-ask',
      question: continueQuestion.trim(),
      originalText: selectedText,
      originalAction: actionType
    });

    // 关闭弹窗
    handleClose();
  };

  // 处理输入框回车事件
  const handleContinueInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendContinueQuestion();
    }
  };

  // 调整
  const handleAdjust = () => {
    // 显示调整选项或重新生成
    setShowContinueInput(true);
    setContinueQuestion('请调整上述内容，使其更加...');
  };

  // 弃用
  const handleDiscard = () => {
    // 关闭弹窗，不保存结果
    textOperations.clearCache();
    handleClose();
  };

  // 插入到下方
  const handleInsertBelow = async () => {
    if (!state.content) {
      console.warn('No content to insert');
      return;
    }

    try {
      const result = textOperations.insertTextBelow(state.content);
      if (result.success) {
        console.log('Text inserted successfully:', result.message);
        // 可以显示成功提示
        handleClose();
      } else {
        console.error('Insert failed:', result.error);
        // 可以显示错误提示
        alert(`插入失败: ${result.error}`);
      }
    } catch (error) {
      console.error('Insert operation failed:', error);
      alert('插入操作失败，请重试');
    }
  };

  // 替换原文
  const handleReplace = async () => {
    if (!state.content) {
      console.warn('No content to replace with');
      return;
    }

    try {
      const result = textOperations.replaceSelectedText(state.content);
      if (result.success) {
        console.log('Text replaced successfully:', result.message);
        // 可以显示成功提示
        handleClose();
      } else {
        console.error('Replace failed:', result.error);
        // 可以显示错误提示
        alert(`替换失败: ${result.error}`);
      }
    } catch (error) {
      console.error('Replace operation failed:', error);
      alert('替换操作失败，请重试');
    }
  };

  // 处理二级操作
  const handleSecondaryAction = (action: SecondaryAction) => {
    switch (action.id) {
      case 'continue-asking':
        handleContinueAsk();
        break;
      case 'adjust':
        handleAdjust();
        break;
      case 'deprecate':
        handleDiscard();
        break;
      case 'insert-below':
        handleInsertBelow();
        break;
      case 'replace-original':
        handleReplace();
        break;
      default:
        console.warn('未知的二级操作:', action);
    }
  };

  // 公开的数据更新方法
  const updateModalData = (data: string, isComplete: boolean) => {
    setState(prev => ({
      ...prev,
      content: data,
      isComplete: isComplete,
      isProcessing: !isComplete,
    }));

    if (isComplete && timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  // 将数据更新方法注册给父组件
  useEffect(() => {
    if (onRegisterDataUpdater) {
      onRegisterDataUpdater(updateModalData);
    }
  }, [onRegisterDataUpdater]);

  // 当组件显示时开始处理
  useEffect(() => {
    const processKey = `${actionType}-${selectedText}`;

    if (isVisible && selectedText && actionType && processStartedRef.current !== processKey) {
      processStartedRef.current = processKey;

      // 缓存当前选择状态，用于后续的文本操作
      textOperations.cacheCurrentSelection();

      // 开始处理（不再调用 AI 服务，而是等待事件数据）
      startProcess();
    }

    return () => {
      if (!isVisible) {
        processStartedRef.current = '';
        stopProcess();
        // 清理缓存的选择状态
        textOperations.clearCache();
      }
    };
  }, [isVisible, selectedText, actionType]);


  if (!isVisible && !isClosing) return null;

  return (
    <div
      className={`${styles.modalOverlay} ${isClosing ? styles.closing : ''}`}
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y, // 现在AIProcessModal独立显示，不需要减去SelectionBar高度
      }}
      onClick={(e) => {
        // 只有当点击的是overlay本身时才关闭弹窗
        if (e.target === e.currentTarget) {
          handleClose();
        }
      }}
    >
      <div
        ref={modalRef}
        className={`${styles.modal} ${isClosing ? styles.closing : ''}`}
        onClick={(e) => {
          // 统一在modal容器级别阻止事件冒泡到overlay
          // 这样点击modal内的任何内容都不会关闭弹窗
          e.stopPropagation();
        }}
      >
        {/* 头部 */}
        <div className={styles.modalHeader}>
          <div className={styles.headerLeft}>
            <div className={styles.aiIcon}>
              <img src={chatLogo} alt="AI助手" />
            </div>
            <span className={styles.actionTitle}>{getActionName(actionType)}</span>
          </div>
          <button
            className={styles.closeButton}
            onClick={handleClose}
          >
            ×
          </button>
        </div>
        {/* 错误状态 */}
        {state.error && (
          <div className={styles.errorMessage}>
            处理失败：{state.error}
          </div>
        )}

        {/* 主要内容区域 */}
        <div className={styles.contentArea}>
          {state.content && (
            <div className={styles.resultContent}>
              {state.content}
              {state.isProcessing && <span className={styles.cursor}>|</span>}
            </div>
          )}

          {state.isComplete && state.content && (
            <div className={styles.contentFooter}>
              <span className={styles.contentStats}>已生成内容 {state.content.length} 字</span>
            </div>
          )}
        </div>

        {/* 继续问输入框 - 水平布局 */}
        {showContinueInput && (
          <>
            <Divider style={{ margin: '16px 0 12px 0' }} />
            <div className={styles.continueInputSection}>
              <div className={styles.continueInputHorizontal}>
                {/* 左侧返回按钮 */}
                <button
                  className={styles.backButton}
                  onClick={() => setShowContinueInput(false)}
                >
                  <LeftOutlined className={styles.backIcon} />
                  <span className={styles.backText}>返回</span>
                </button>

                {/* 右侧输入框 */}
                <div className={styles.inputContainer}>
                  <input
                    className={styles.continueInputHorizontalInput}
                    value={continueQuestion}
                    onChange={(e) => setContinueQuestion(e.target.value)}
                    onKeyDown={handleContinueInputKeyDown}
                    placeholder="请输入您想继续询问的问题..."
                    autoFocus
                  />
                </div>
              </div>
            </div>
          </>
        )}

        {/* 底部操作按钮 */}
        {state.isComplete && !state.error && !showContinueInput && (
          <SecondaryActionButtons
            menuItemId={actionType}
            onAction={handleSecondaryAction}
            disabled={state.isProcessing}
          />
        )}
      </div>
    </div>
  );
};

export default AIProcessModal;
