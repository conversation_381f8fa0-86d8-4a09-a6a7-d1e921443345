import { useState } from 'react'

import Routing from './routes'
import './style.less'
import SelectionHandler from './components/SelectionHandler'
import { message } from 'antd'

export default () => {
  const [selectedText, setSelectedText] = useState<string>('')
  const [textOperation, setTextOperation] = useState<string>('')

  // 处理从划词工具栏接收到的文本（仅用于"打开面板"操作）
  const handleTextSelected = (text: string, operation: string) => {
    setSelectedText(text)
    if (operation === '打开面板') {
      setTextOperation('')
      message.info('已接收选中文本')
    }
  }

  // 处理继续问请求
  const handleContinueAsk = (
    question: string,
    originalText: string,
    originalAction: string,
    conversationId?: string
  ) => {
    setSelectedText(question)
    setTextOperation('继续问')
    message.info('收到继续问请求')

    // 这里可以进一步处理继续问逻辑
    console.log('Continue ask:', {
      question,
      originalText,
      originalAction,
      conversationId
    })
  }

  return (
    <div className="side-panel">
      <SelectionHandler
        onTextSelected={handleTextSelected}
        onContinueAsk={handleContinueAsk}
      />
      <Routing selectedText={selectedText} textOperation={textOperation} />
    </div>
  )
}
