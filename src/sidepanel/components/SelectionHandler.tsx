import React, { useEffect } from 'react';

interface SelectionHandlerProps {
  onTextSelected?: (text: string, operation: string) => void;
  onContinueAsk?: (question: string, originalText: string, originalAction: string, conversationId?: string) => void;
}

const SelectionHandler: React.FC<SelectionHandlerProps> = ({ onTextSelected, onContinueAsk }) => {
  useEffect(() => {
    // 监听来自背景脚本的消息
    const messageListener = (message: any) => {
      if (message.type === 'SELECTED_TEXT' && message.text) {
        // 当收到选中的文本时，通知父组件（仅用于"打开面板"操作）
        onTextSelected?.(message.text, '打开面板');
      } else if (message.type === 'CONTINUE_ASK_TO_SIDEBAR' && message.question) {
        // 当收到继续问的请求时，使用专门的回调
        onContinueAsk?.(
          message.question,
          message.originalText,
          message.originalAction,
          message.conversationId
        );
      }
      // 移除 PROCESS_TEXT 处理，因为AI操作现在直接在content script中处理
    };

    chrome.runtime.onMessage.addListener(messageListener);

    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, [onTextSelected, onContinueAsk]);

  // 这个组件不渲染任何内容，只是作为消息处理器
  return null;
};

export default SelectionHandler;