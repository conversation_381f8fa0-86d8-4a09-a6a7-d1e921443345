import '@ht/chatui/dist/index.css'

import React, { useRef, Suspense, useEffect } from 'react'
import { init, log } from '@ht/xlog'
import { isPrd } from '@src/common/utils'
import { message } from 'antd/es'
import GuidePage from './GuidePage'
import FooterVersion from './FooterVersion'
import Loading from './Loading'
import ChatUI from '@ht/chatui'
import { EFloatButtonActionType } from '@src/common/const'
import { getAllActions } from '@src/common/actions'
import { createComposerConfig, config } from '../../../config/aiChatConfig'
import { handleSummaryCurrentPage } from './utils'
import './style.less'
import { SettingIcon } from '@src/common/Icons'

export default () => {
  const chatUiRef = useRef(null)
  const [messageApi, contextHolder] = message.useMessage()
  const actions = getAllActions()

  // 创建 composerConfig，传递 messageApi 和 chatUiRef
  const composerConfig = createComposerConfig({ messageApi, chatUiRef })
  // 初始化日志
  const initLog = () => {
    init({
      uuid: 'anonymous_user',
      from: 'HtscAiExtension',
      types: ['fetch', 'unhandledrejection', 'windowError'],
      myTrackConfig: {
        // 项目信息配置，数据是上报到数智中台，需要去数智中台申请一个项目（product_id和product_name）
        product_id: '366',
        product_name: 'Web开发平台',
        channel_env: isPrd ? 'prd_outer' : 'prd_outer_test', // 上报环境
      },
    })
  }

  useEffect(() => {
    initLog()
  }, [])


  useEffect(() => {
    // 监听来自背景脚本的消息
    const messageListener = (message: any) => {
      console.log('Sidepanel received message:', message);

      // 处理悬浮球消息
      if (message.type === EFloatButtonActionType.Summary) {
        handleSummaryCurrentPage(messageApi, chatUiRef)
      }
      // 处理继续问消息
      else if (message.type === 'CONTINUE_ASK_TO_SIDEBAR' && message.question) {
        console.log('Received continue ask:', {
          question: message.question,
          originalText: message.originalText,
          originalAction: message.originalAction,
          conversationId: message.conversationId
        });

        // 直接发送继续问的问题到聊天
        if (chatUiRef.current?.chatContext?.onSend) {
          chatUiRef.current.chatContext.onSend('text', message.question, {
            conversationId: message.conversationId,
            // extendParams: {
            //   msgChannel: 'continueAsk'
            // }
          });
        }
        messageApi.info('收到继续问请求');
      }
    };

    chrome.runtime.onMessage.addListener(messageListener);

    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, [messageApi]);

  type TRenderWelcomeReturnType = React.ReactNode &
    React.ForwardRefExoticComponent<any>

  const onReportLog = (params: any) => {
    const { id, page_id, page_title, btn_id, btn_title } = params
    if (id) {
      log({
        id,
        page_id,
        page_title,
        btn_id,
        btn_title,
      })
    }
  }
  // 打开配置页面
  const handleSettingsClick = async () => {
    try {
      // 在新标签页中打开配置页面
      // 使用与upgrade页面相同的路径格式
      const settingsUrl = `chrome-extension://${chrome.runtime.id}/tabs/settings.html`

      console.log('Opening settings URL:', settingsUrl)
      await chrome.tabs.create({
        url: settingsUrl,
      })
    } catch (error) {
      console.error('Failed to open settings page:', error)
      // 如果打开新标签页失败，则显示错误信息
      console.error('Settings page navigation failed')
    }
  }



  return (
    <Suspense fallback={<Loading />}>
      <div className='ChatWrap'>
        <div className='setIcon' onClick={handleSettingsClick}>
          <SettingIcon />
        </div>
        <ChatUI
          navbar={{
            showLogo: false,
            showCloseButton: false,
            title: '',
          }}
          renderNavbar={() => null}
          operationConfig={{
            text: [],
            web: [],
            image: [],
            file: []
          }}
          historyConversation={{}}
          messageContainerConfig={{}}
          ref={chatUiRef}
          config={config}
          actions={actions}
          renderWelcome={(props) =>
            (<GuidePage {...props} />) as TRenderWelcomeReturnType
          }
          onReportLog={onReportLog}
          inputOptions={{
            minRows: 2,
          }}
          composerConfig={composerConfig}
          renderFooterVersion={() => <FooterVersion />}
          showStopAnswer={true}
          showToken={false} // 不展示消耗的token数量
          showHallucination={false} // 不展示合规话术
        />
        {contextHolder}
      </div>
    </Suspense>
  )
}
